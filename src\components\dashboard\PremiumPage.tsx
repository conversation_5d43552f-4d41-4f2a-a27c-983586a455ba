import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import { useDashboard } from '../../contexts/DashboardContext';
import Card from '../common/Card';
import Button from '../common/Button';
import { Save } from 'lucide-react';

const PremiumAnalysisForm = () => {
  // Initialize form state
  const [formData, setFormData] = useState({
    currentPremium: '',
    changePremium: false,
    newPremiumAmount: '',
    newPremiumStartAge: '',
    lumpSumPremium: '',
    singlePayPremium: '',
    singlePayPaidInYear: '',
    limitedPayPremium: '',
    limitedPayYears: '',
    changeYearToYear: false,
    continueScheduled: true,
    stopPremiumNow: false,
    stopPremiumFromAge: false,
    modelInterestRates: false,
    currentCreditingRate: '',
    guaranteedMinRate: '',
    stressScenarioRate: '',
    excessEarningsRate: '',
    includeRiderChanges: false,
    stopPremiumPayments: false,
    stopAge: '',
    stopYear: '',
    stopOtherAge: '',
    stopCashValue: '',
    modelLevelPremium: false,
    levelPremiumPercentage: '',
    levelPremiumAmount: '',
    levelPremiumGoal: '',
    levelPremiumYears: '',
    levelPremiumAge: '',
    levelPremiumMaximize: false,
    levelPremiumEndAge: '',
    noLevelPremium: true,
    catchUpPremium: false,
    catchUpAmount: '',
    catchUpYears: '',
    catchUpOverYears: '',
    catchUpStartPolicyYear: '',
    catchUpStartAge: '',
    catchUpTarget: '',
    catchUpCashValue: '',
    noCatchUp: true,
    cashValueTarget: ''
  });

  // Dashboard context for tab and customer selection
  const { setActiveTab, selectedCustomerData, selectedPolicyData } = useDashboard();

  // Analysis results state
  type ProjectionData = {
    year: number;
    basePremium: number;
    modifiedPremium: number;
    baseCashValue: number;
    modifiedCashValue: number;
  };
  const [analysisResults, setAnalysisResults] = useState<ProjectionData[] | null>(null);
  const [showReport, setShowReport] = useState(false);

  // Handle form input changes
  const handleInputChange = (name: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // Handle checkbox changes
  const handleCheckboxChange = (name: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Handle radio button changes for stop premium options
  const handleStopPremiumRadio = (option: string) => {
    setFormData(prev => ({
      ...prev,
      stopPremiumNow: option === 'now',
      stopPremiumFromAge: option === 'fromAge'
    }));
  };

  // Generate sample projection data
  const generateProjectionData = () => {
    const years = Array.from({ length: 20 }, (_, i) => i + 1);
    const currentPremium = parseFloat(formData.currentPremium) || 5000;

    return years.map(year => {
      let basePremium = currentPremium;
      let modifiedPremium = currentPremium;
      let baseCashValue = currentPremium * year * 0.8;
      let modifiedCashValue = currentPremium * year * 0.8;

      // Apply new premium amount
      if (formData.newPremiumAmount) {
        const newPremium = parseFloat(formData.newPremiumAmount);
        const startAge = parseInt(formData.newPremiumStartAge) || 1;
        const currentAge = 40; // Assume current age
        const startYear = Math.max(1, startAge - currentAge + 1);

        if (year >= startYear) {
          modifiedPremium = newPremium;
          modifiedCashValue = baseCashValue + (newPremium - currentPremium) * (year - startYear + 1) * 0.8;
        }
      }

      // Apply lump sum premium
      if (formData.lumpSumPremium) {
        const lumpSum = parseFloat(formData.lumpSumPremium);
        if (year === 1) {
          modifiedPremium = lumpSum;
        } else {
          modifiedPremium = 0;
        }
        modifiedCashValue = lumpSum * Math.pow(1.04, year - 1);
      }

      // Apply stop premium payments
      if (formData.stopPremiumNow) {
        modifiedPremium = 0;
        modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
      } else if (formData.stopPremiumFromAge && formData.stopAge) {
        const stopAge = parseInt(formData.stopAge);
        const currentAge = 40; // Assume current age
        const stopYear = stopAge - currentAge + 1;
        if (year >= stopYear) {
          modifiedPremium = 0;
          modifiedCashValue = baseCashValue * 0.95; // Assume some decline without premiums
        }
      }

      // Apply cash value target adjustments
      if (formData.cashValueTarget) {
        const target = parseFloat(formData.cashValueTarget);
        const targetYear = 10; // Assume target in 10 years
        if (year <= targetYear) {
          const requiredGrowth = target / targetYear;
          modifiedCashValue = Math.max(modifiedCashValue, requiredGrowth * year);
        }
      }

      return {
        year,
        basePremium,
        modifiedPremium,
        baseCashValue,
        modifiedCashValue
      };
    });
  };

  // Run analysis
  const runAnalysis = () => {
    const projectionData = generateProjectionData();
    setAnalysisResults(projectionData);
  };

  // Generate report
  const generateReport = () => {
    runAnalysis();
    setShowReport(true);
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      currentPremium: '',
      changePremium: false,
      newPremiumAmount: '',
      newPremiumStartAge: '',
      lumpSumPremium: '',
      singlePayPremium: '',
      singlePayPaidInYear: '',
      limitedPayPremium: '',
      limitedPayYears: '',
      changeYearToYear: false,
      continueScheduled: true,
      stopPremiumNow: false,
      stopPremiumFromAge: false,
      modelInterestRates: false,
      currentCreditingRate: '',
      guaranteedMinRate: '',
      stressScenarioRate: '',
      excessEarningsRate: '',
      includeRiderChanges: false,
      stopPremiumPayments: false,
      stopAge: '',
      stopYear: '',
      stopOtherAge: '',
      stopCashValue: '',
      modelLevelPremium: false,
      levelPremiumPercentage: '',
      levelPremiumAmount: '',
      levelPremiumGoal: '',
      levelPremiumYears: '',
      levelPremiumAge: '',
      levelPremiumMaximize: false,
      levelPremiumEndAge: '',
      noLevelPremium: true,
      catchUpPremium: false,
      catchUpAmount: '',
      catchUpYears: '',
      catchUpOverYears: '',
      catchUpStartPolicyYear: '',
      catchUpStartAge: '',
      catchUpTarget: '',
      catchUpCashValue: '',
      noCatchUp: true,
      cashValueTarget: ''
    });
    setAnalysisResults(null);
    setShowReport(false);
  };

  // --- Customer Info Extraction (similar to AsIsPage) ---
  let customerInfo = null;
  if (selectedCustomerData && selectedPolicyData) {
    // Extract premium amount from string (e.g., "2000 $ annually" -> "2000")
    const premiumMatch = selectedPolicyData.premium.match(/(\d+)/);
    const premiumAmount = premiumMatch ? premiumMatch[1] : '';
    // Extract coverage amount from string (e.g., "500,000 $" -> "500000")
    const coverageMatch = selectedPolicyData.coverage.replace(/,/g, '').match(/(\d+)/);
    const coverageAmount = coverageMatch ? coverageMatch[1] : '';
    // Calculate current age from DOB (assuming DD.MM.YYYY)
    const calculateAge = (dobString: string): string => {
      const [day, month, year] = dobString.split('.').map(Number);
      const birthDate = new Date(year, month - 1, day);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age.toString();
    };
    customerInfo = {
      policyNumber: selectedCustomerData.details["Policy Number"] || selectedCustomerData.policyNumber,
      customerName: selectedCustomerData.name,
      customerId: selectedCustomerData.details["Customer ID"] || selectedCustomerData.customerId,
      policyType: selectedPolicyData.name,
      faceAmount: coverageAmount,
      annualPremium: premiumAmount,
      paymentPeriod: '20', // Default or extract if available
      dividendOption: 'Paid-up Additions', // Default or extract if available
      currentAge: calculateAge(selectedCustomerData.details.DOB),
      retirementAge: '65',
      lifeExpectancy: '85',
    };
  }

  // --- Render ---
  return (
    <div className="w-full max-w-none space-y-6 px-4 py-4">
      {/* Introduction Text */}
      <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
        <p className="text-lg text-gray-800 leading-relaxed">
          You may want to change the annual premium for making, or lower outgo, single payment, or to
          increase to accrue more cash value, shorten the payment period or pay catch-up premiums to restore
          or strengthen your policy's performance or model level premium. Your scenarios will be displayed for
          Current Interest Rate.
        </p>
      </div>





      {/* Show message if no policy is selected */}
      {(!selectedCustomerData || !selectedPolicyData) ? (
        <Card className="bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/40 rounded-full flex items-center justify-center">
              <span className="text-yellow-600 dark:text-yellow-400 text-sm">!</span>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-yellow-800 dark:text-yellow-200">No Policy Selected</h3>
              <p className="text-yellow-700 dark:text-yellow-300">
                Please go to the Policy Selection tab first to search and select a customer policy before configuring the Premium illustration.
              </p>
              <Button
                onClick={() => setActiveTab('policy-selection')}
                variant="outline"
                className="mt-3 border-yellow-300 text-yellow-700 hover:bg-yellow-100 dark:border-yellow-600 dark:text-yellow-300 dark:hover:bg-yellow-900/40"
              >
                Go to Policy Selection
              </Button>
            </div>
          </div>
        </Card>
      ) : (
        <div className="space-y-8">
          {/* Section 1: Premium Changes */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">1. Premium Changes</h3>

            <div className="space-y-6">
              {/* Current Premium Display */}
              <div className="mb-6">
                <label className="block text-lg font-semibold text-black mb-2">
                  Your current annual premium is:
                  <span className="ml-2 text-black font-bold text-xl">
                    ${formData.currentPremium || '_______'}
                  </span>
                </label>
                <input
                  type="text"
                  value={formData.currentPremium}
                  onChange={(e) => handleInputChange('currentPremium', e.target.value)}
                  placeholder="Enter current premium amount"
                  className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg text-lg font-semibold focus:border-blue-500 focus:outline-none bg-white text-black mt-2"
                />
              </div>

              {/* Question: Do you want to change it? */}
              <div className="mb-4">
                <p className="text-lg font-semibold text-black mb-4">Do you want to change it?</p>

                {/* Option A: Change to New Premium Amount */}
                <div className="space-y-4">
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <span className="text-black font-bold">a.</span>
                      <div className="flex-1">
                        <span>Change to New premium amount</span>
                        <div className="mt-2 flex items-center space-x-4">
                          <div>
                            <span className="text-black mr-2">$</span>
                            <input
                              type="text"
                              value={formData.newPremiumAmount}
                              onChange={(e) => handleInputChange('newPremiumAmount', e.target.value)}
                              placeholder="____"
                              className="w-32 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                          </div>
                          <span className="text-black">now or starting from age</span>
                          <select
                            value={formData.newPremiumStartAge}
                            onChange={(e) => handleInputChange('newPremiumStartAge', e.target.value)}
                            className="px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                          >
                            <option value="">Select Age</option>
                            {Array.from({ length: 50 }, (_, i) => i + 25).map(age => (
                              <option key={age} value={age}>{age}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </label>
                  </div>

                  {/* Option B: Vary Amount by Age */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <span className="text-black font-bold">b.</span>
                      <span>Vary the Amount by Age</span>
                      <Button
                        onClick={() => alert('Schedule feature coming soon!')}
                        variant="outline"
                        className="ml-4 px-4 py-2 text-sm bg-gray-100 border-gray-300 text-black hover:bg-gray-200"
                      >
                        SCHEDULE BUTTON
                      </Button>
                    </label>
                  </div>

                  {/* Option C: Lump Sum */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <span className="text-black font-bold">c.</span>
                      <div className="flex-1">
                        <span>Lump Sum (One-time premium):</span>
                        <div className="mt-2 flex items-center space-x-2">
                          <span className="text-black">$</span>
                          <input
                            type="text"
                            value={formData.lumpSumPremium}
                            onChange={(e) => handleInputChange('lumpSumPremium', e.target.value)}
                            placeholder="________"
                            className="w-40 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                          />
                          <span className="text-black">now</span>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 2: Stop Premium Payments */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">2. Stop Premium Payments</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <p className="text-lg font-semibold text-black mb-4">
                  Do you want to stop paying future premiums and see how long the policy remains in force?
                  Or Do you want model policy lapse age/year by stopping premiums?
                </p>

                <div className="space-y-4">
                  {/* Option: Stop Now */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="stopPremiumOption"
                        checked={formData.stopPremiumNow}
                        onChange={() => handleStopPremiumRadio('now')}
                        className="w-5 h-5 text-black"
                      />
                      <span>Now</span>
                    </label>
                  </div>

                  {/* Option: Stop Starting from Age */}
                  <div className="pl-4">
                    <label className="flex items-start space-x-3 text-lg font-semibold text-black">
                      <input
                        type="radio"
                        name="stopPremiumOption"
                        checked={formData.stopPremiumFromAge}
                        onChange={() => handleStopPremiumRadio('fromAge')}
                        className="w-5 h-5 text-black mt-1"
                      />
                      <div className="flex-1">
                        <span>Starting from age</span>
                        <div className="mt-2">
                          <select
                            value={formData.stopAge}
                            onChange={(e) => handleInputChange('stopAge', e.target.value)}
                            className="px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            disabled={!formData.stopPremiumFromAge}
                          >
                            <option value="">Select Age</option>
                            {Array.from({ length: 50 }, (_, i) => i + 25).map(age => (
                              <option key={age} value={age}>{age}</option>
                            ))}
                          </select>
                        </div>
                      </div>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Section 3: Interest Rate Scenarios */}
          <div className="bg-white p-6 rounded-xl shadow-lg border border-gray-200">
            <h3 className="text-xl font-bold text-black mb-6 pb-3 border-b-2 border-gray-200">3. Interest Rate Scenarios</h3>

            <div className="space-y-6">
              <div className="mb-4">
                <p className="text-lg font-semibold text-black mb-4">
                  Do you want to model different interest / crediting rate scenarios for the policy?
                </p>

                <div className="space-y-4">
                  {/* Option: Yes, model different scenarios */}
                  <div className="pl-4">
                    <label className="flex items-center space-x-3 text-lg font-semibold text-black">
                      <input
                        type="checkbox"
                        checked={formData.modelInterestRates}
                        onChange={(e) => handleCheckboxChange('modelInterestRates', e.target.checked)}
                        className="w-5 h-5 text-black"
                      />
                      <span>Yes, model different interest/crediting rate scenarios for the policy.</span>
                    </label>
                  </div>

                  {formData.modelInterestRates && (
                    <div className="ml-8 space-y-4 pl-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <label className="block text-black font-semibold mb-2">Current interest/crediting rate:</label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={formData.currentCreditingRate}
                              onChange={(e) => handleInputChange('currentCreditingRate', e.target.value)}
                              placeholder="_____"
                              className="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                            <span className="text-black font-semibold">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-black font-semibold mb-2">Guaranteed minimum rate:</label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={formData.guaranteedMinRate}
                              onChange={(e) => handleInputChange('guaranteedMinRate', e.target.value)}
                              placeholder="_____"
                              className="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                            <span className="text-black font-semibold">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-black font-semibold mb-2">Stress scenario rate:</label>
                          <div className="flex items-center space-x-2">
                            <input
                              type="text"
                              value={formData.stressScenarioRate}
                              onChange={(e) => handleInputChange('stressScenarioRate', e.target.value)}
                              placeholder="_____"
                              className="w-24 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none bg-white text-black"
                            />
                            <span className="text-black font-semibold">%</span>
                          </div>
                        </div>

                        <div>
                          <label className="block text-black font-semibold mb-2">User-defined Rates</label>
                          <Button
                            onClick={() => alert('Schedule feature coming soon!')}
                            variant="outline"
                            className="px-4 py-2 text-sm bg-gray-100 border-gray-300 text-black hover:bg-gray-200"
                          >
                            Schedule
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4 justify-center mt-8">
            <Button
              onClick={() => alert('Analysis saved to history!')}
              variant="primary"
              className="flex items-center space-x-2 bg-indigo-600 hover:bg-indigo-700 text-white shadow-lg border-none"
            >
              <Save className="w-4 h-4" />
              <span>Save Premium Analysis</span>
            </Button>
            <Button
              onClick={resetForm}
              variant="primary"
              className="flex items-center space-x-2 bg-gray-600 hover:bg-gray-700 text-white shadow-lg border-none"
            >
              <span>Reset</span>
            </Button>
          </div>
        </div>
      )}

      {/* Analysis Results */}
      {analysisResults && (
        <div className="mt-8 bg-gradient-to-r from-green-50 to-green-100 p-6 rounded-xl shadow-lg border-l-4 border-green-500">
          <h3 className="text-xl font-bold text-green-800 mb-6 text-center">📊 Premium Analysis Results</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Premium Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Premium Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="basePremium" stroke="#2563eb" strokeWidth={2} name="Current Premium" />
                  <Line type="monotone" dataKey="modifiedPremium" stroke="#dc2626" strokeWidth={2} name="Modified Premium" />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Cash Value Projection Chart */}
            <div>
              <h4 className="text-lg font-bold text-black mb-4">Cash Value Projection Comparison</h4>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={analysisResults}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="year" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="baseCashValue" stroke="#16a34a" strokeWidth={2} name="Current Cash Value" />
                  <Line type="monotone" dataKey="modifiedCashValue" stroke="#ea580c" strokeWidth={2} name="Modified Cash Value" />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Analysis Summary */}
          <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
            <h4 className="text-lg font-bold text-black mb-4">Analysis Summary</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {formData.currentPremium && (
                <div className="text-black">
                  <strong>Current Annual Premium:</strong> ${formData.currentPremium}
                </div>
              )}
              {formData.newPremiumAmount && (
                <div className="text-black">
                  <strong>New Premium Amount:</strong> ${formData.newPremiumAmount}
                  {formData.newPremiumStartAge && ` starting from age ${formData.newPremiumStartAge}`}
                </div>
              )}
              {formData.lumpSumPremium && (
                <div className="text-black">
                  <strong>Lump Sum Premium:</strong> ${formData.lumpSumPremium}
                </div>
              )}
              {formData.stopPremiumNow && (
                <div className="text-black">
                  <strong>Stop Premium:</strong> Now
                </div>
              )}
              {formData.stopPremiumFromAge && formData.stopAge && (
                <div className="text-black">
                  <strong>Stop Premium at Age:</strong> {formData.stopAge}
                </div>
              )}
              {formData.cashValueTarget && (
                <div className="text-black">
                  <strong>Cash Value Target:</strong> ${formData.cashValueTarget}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Comprehensive Report */}
      {showReport && (
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl shadow-lg border-l-4 border-blue-500">
          <h3 className="text-xl font-bold text-blue-800 mb-6 text-center">📈 Comprehensive Premium Analysis Report</h3>
          
          <div className="space-y-6">
            {/* Report Header */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Report Details</h4>
              <p><strong>Report Generated:</strong> {new Date().toLocaleString()}</p>
              <p><strong>Customer:</strong> {selectedCustomerData?.name || 'N/A'}</p>
              <p><strong>Policy Number:</strong> {selectedCustomerData?.details?.["Policy Number"] || selectedCustomerData?.policyNumber || 'N/A'}</p>
              <p><strong>Policy Type:</strong> {selectedPolicyData?.name || 'N/A'}</p>
            </div>

            {/* Executive Summary */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Executive Summary</h4>
              <ul className="space-y-2 text-black">
                {formData.currentPremium && <li>• Current annual premium: ${formData.currentPremium}</li>}
                {formData.newPremiumAmount && <li>• New premium amount: ${formData.newPremiumAmount}{formData.newPremiumStartAge && ` starting from age ${formData.newPremiumStartAge}`}</li>}
                {formData.lumpSumPremium && <li>• Lump sum premium: ${formData.lumpSumPremium}</li>}
                {formData.stopPremiumNow && <li>• Analysis includes immediate premium payment cessation</li>}
                {formData.stopPremiumFromAge && formData.stopAge && <li>• Analysis includes premium payment cessation from age ${formData.stopAge}</li>}
                {formData.cashValueTarget && <li>• Cash value target: ${formData.cashValueTarget}</li>}
              </ul>
            </div>

            {/* Recommendations */}
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <h4 className="text-lg font-bold text-black mb-3">Recommendations</h4>
              <ul className="space-y-2 text-black">
                {(formData.stopPremiumNow || formData.stopPremiumFromAge) && <li>• Consider the policy lapse risk when stopping premium payments</li>}
                {formData.lumpSumPremium && <li>• Lump sum option eliminates future premium payment uncertainty</li>}
                {formData.newPremiumAmount && <li>• Premium adjustments can help optimize policy performance</li>}
                {formData.cashValueTarget && <li>• Setting cash value targets helps achieve financial goals</li>}
                <li>• Regular policy reviews are recommended to monitor performance</li>
                <li>• Consider current interest rate environment when making premium decisions</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PremiumAnalysisForm;